<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\OrderItem;

class OrderModification extends Model
{
    protected $fillable = [
        'order_id',
        'type',
        'order_item_id',
        'item_name',
        'old_quantity',
        'new_quantity',
        'old_price',
        'new_price',
        'reason'
    ];

    protected $casts = [
        'old_quantity' => 'integer',
        'new_quantity' => 'integer',
        'old_price' => 'decimal:2',
        'new_price' => 'decimal:2',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($modification) {
            // Ensure item_name is never null
            if (empty($modification->item_name)) {
                if ($modification->order_item_id) {
                    $orderItem = OrderItem::find($modification->order_item_id);
                    $modification->item_name = $orderItem ? ($orderItem->title ?? $orderItem->name ?? 'Unknown Item') : 'Unknown Item';
                } else {
                    $modification->item_name = 'Unknown Item';
                }
            }
        });
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItem::class);
    }
}