<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_modifications', function (Blueprint $table) {
            $table->dropForeign(['item_id']);
        });

        DB::statement("
            UPDATE order_modifications 
            SET item_id = NULL 
            WHERE item_id IS NOT NULL 
            AND item_id NOT IN (SELECT id FROM order_item)
        ");

        Schema::table('order_modifications', function (Blueprint $table) {
            $table->renameColumn('item_id', 'order_item_id');
        });

        Schema::table('order_modifications', function (Blueprint $table) {
            $table->foreign('order_item_id')->references('id')->on('order_item')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_modifications', function (Blueprint $table) {
            // Drop the new foreign key constraint
            $table->dropForeign(['order_item_id']);
            
            // Rename the column back from order_item_id to item_id
            $table->renameColumn('order_item_id', 'item_id');
            
            // Add back the original foreign key constraint to item table
            $table->foreign('item_id')->references('id')->on('item')->onDelete('set null');
        });
    }
};