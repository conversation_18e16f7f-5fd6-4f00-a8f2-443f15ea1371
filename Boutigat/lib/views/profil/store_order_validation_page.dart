import 'dart:async';
import 'dart:ui';

import 'package:boutigak/views/profil/boutigak/my_store_orders_page.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:url_launcher/url_launcher.dart';

class StoreOrderValidationPage extends StatefulWidget {
  final Order order;
  const StoreOrderValidationPage({super.key, required this.order});

  @override
  State<StoreOrderValidationPage> createState() =>
      _StoreOrderValidationPageState();
}

class _StoreOrderValidationPageState extends State<StoreOrderValidationPage> {
  final OrderController orderController = Get.find<OrderController>();
  bool isValidating = false;
  bool isDeclining = false;

  @override
  void initState() {
    super.initState();
    // Clear any existing modifications for this order when entering the page
    _clearOrderModifications();
    // Refresh the order data to ensure we have the latest item information
    _refreshOrderData();
  }

  void _clearOrderModifications() {
    final orderId = widget.order.orderId;
    if (orderId != null) {
      orderController.clearOrderModifications(orderId);
    }
  }

  void _refreshOrderData() async {
    // Refresh the store orders to get the latest item data
    try {
      print('Refreshing order data for order ${widget.order.orderId}');
      print('Current order items count: ${widget.order.items.length}');
      for (var item in widget.order.items) {
        print('Item ${item.itemId}: title=${item.title}, name=${item.name}, images=${item.images?.length ?? 0}');
      }

      await orderController.fetchMyStoreOrders();

      // Find the updated order
      final updatedOrder = orderController.myStoreOrders.firstWhere(
        (order) => order.orderId == widget.order.orderId,
        orElse: () => widget.order,
      );

      print('After refresh - items count: ${updatedOrder.items.length}');
      for (var item in updatedOrder.items) {
        print('Updated Item ${item.itemId}: title=${item.title}, name=${item.name}, images=${item.images?.length ?? 0}');
      }
    } catch (e) {
      print('Error refreshing order data: $e');
    }
  }

  // ---------- Helpers ----------
  Order get _currentOrder {
    // Try to get the updated order from the controller
    try {
      return orderController.myStoreOrders.firstWhere(
        (order) => order.orderId == widget.order.orderId,
      );
    } catch (e) {
      // If not found in controller, use the original order
      return widget.order;
    }
  }

  List<OrderItem> _nonRemovedItems() {
    final currentOrder = _currentOrder;
    return currentOrder.items
        .where((it) =>
            !orderController.isItemRemoved(currentOrder.orderId!, it.itemId))
        .toList();
  }

  int _effectiveQty(OrderItem it) {
    final key = '${widget.order.orderId}_${it.itemId}';
    return orderController.modifiedQuantities[key] ?? it.quantity;
  }

  double _productsTotal(List<OrderItem> items) {
    double sum = 0;
    for (final it in items) {
      final price = (it.price ?? 0).toDouble();
      sum += price * _effectiveQty(it);
    }
    return sum;
  }

  Future<void> _callNumber(String phone) async {
    final uri = Uri(scheme: 'tel', path: phone.replaceAll(' ', ''));
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      Get.snackbar(
        'error'.tr.isEmpty ? 'Error' : 'error'.tr,
        'cannot_place_call'.tr.isEmpty
            ? 'Cannot place a call on this device.'
            : 'cannot_place_call'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showRemoveItemDialog(int itemId) {
    Get.dialog(
      AlertDialog(
        title:
            Text('remove_item'.tr.isEmpty ? 'Remove Item' : 'remove_item'.tr),
        content: Text('remove_item_confirm'.tr.isEmpty
            ? 'Are you sure you want to remove this item from the order?'
            : 'remove_item_confirm'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr.isEmpty ? 'Cancel' : 'cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              orderController.removeOrderItem(widget.order.orderId!, itemId);
              Get.back();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('remove'.tr.isEmpty ? 'Remove' : 'remove'.tr),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final delivery = double.tryParse(widget.order.deliveryCharge ?? '0') ?? 0.0;

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Bande dégradée
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 230,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withOpacity(0.9)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),

          SafeArea(
            bottom: false,
            child: Column(
              children: [
                // Header glass + menu
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 10),
                  child: _GlassHeader(
                    title:
                        '${'validate'.tr.isEmpty ? 'Validate' : 'validate'.tr} #${widget.order.orderId ?? '—'}',
                  ),
                ),

                // Corps blanc arrondi
                Expanded(
                  child: Obx(() {
                    final items = _nonRemovedItems();
                    final productsTotal = _productsTotal(items);
                    final total = productsTotal + delivery;

                    return Stack(
                      children: [
                        // Scrollable content
                        Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(22)),
                          ),
                          child: CustomScrollView(
                            physics: const BouncingScrollPhysics(),
                            slivers: [
                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(14, 18, 14, 12),
                                sliver: SliverToBoxAdapter(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                          color:
                                              Colors.black12.withOpacity(0.06)),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                          14, 14, 10, 14),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Avatar
                                          Container(
                                            width: 48,
                                            height: 48,
                                            decoration: BoxDecoration(
                                              color: Colors.black12
                                                  .withOpacity(0.06),
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                                Icons.person_outline_rounded,
                                                color: Colors.black87),
                                          ),
                                          const SizedBox(width: 12),

                                          // Infos client
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(children: [
                                                  Expanded(
                                                    child: Text(
                                                      (() {
                                                        final fn = widget.order
                                                                .userFirstName ??
                                                            '';
                                                        final ln = widget.order
                                                                .userLastName ??
                                                            '';
                                                        final full =
                                                            '$fn $ln'.trim();
                                                        return full.isEmpty
                                                            ? ('customer'
                                                                    .tr
                                                                    .isEmpty
                                                                ? 'Customer'
                                                                : 'customer'.tr)
                                                            : full;
                                                      })(),
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          fontSize: 16),
                                                    ),
                                                  ),
                                                  if (widget
                                                      .order.isCashOnDelivery)
                                                    Container(
                                                      margin:
                                                          const EdgeInsets.only(
                                                              left: 8),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 10,
                                                          vertical: 6),
                                                      decoration: BoxDecoration(
                                                        color: Colors.blueGrey
                                                            .withOpacity(0.08),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(999),
                                                        border: Border.all(
                                                            color: Colors
                                                                .blueGrey
                                                                .withOpacity(
                                                                    0.28)),
                                                      ),
                                                      child: Text(
                                                          'cash_on_delivery'.tr,
                                                          style:
                                                              const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w700,
                                                                  fontSize:
                                                                      12)),
                                                    ),
                                                ]),
                                                const SizedBox(height: 8),
                                                Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      if ((widget.order
                                                                  .userPhone ??
                                                              '')
                                                          .isNotEmpty)
                                                        InkWell(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                          onTap: () =>
                                                              _callNumber(widget
                                                                  .order
                                                                  .userPhone!),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        4),
                                                            child: Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .min,
                                                                children: [
                                                                  const Icon(
                                                                      FontAwesomeIcons
                                                                          .phone,
                                                                      size: 18,
                                                                      color: Colors
                                                                          .black54),
                                                                  const SizedBox(
                                                                      width: 6),
                                                                  Text(
                                                                      widget
                                                                          .order
                                                                          .userPhone!,
                                                                      style: const TextStyle(
                                                                          color: Colors
                                                                              .black87,
                                                                          fontWeight:
                                                                              FontWeight.w500)),
                                                                ]),
                                                          ),
                                                        ),
                                                      if ((widget.order
                                                                  .isPaid ||
                                                              widget.order
                                                                  .isCashOnDelivery) &&
                                                          widget.order
                                                                  .deliveryLatitude !=
                                                              null &&
                                                          widget.order
                                                                  .deliveryLongitude !=
                                                              null)
                                                        Container(
                                                          height: 40,
                                                          width: 40,
                                                          margin:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                                  horizontal:
                                                                      3),
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color: Colors
                                                                    .grey
                                                                    .shade300),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                          ),
                                                          child: Material(
                                                            color: Colors
                                                                .transparent,
                                                            child: InkWell(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8),
                                                              onTap: () {
                                                                showDialog(
                                                                  context:
                                                                      context,
                                                                  builder: (_) =>
                                                                      FractionallySizedBox(
                                                                    heightFactor:
                                                                        0.6,
                                                                    child:
                                                                        DeliveryMapDialog(
                                                                      latitude: widget
                                                                          .order
                                                                          .deliveryLatitude!,
                                                                      longitude: widget
                                                                          .order
                                                                          .deliveryLongitude!,
                                                                      subtitle:
                                                                          widget.order.deliveryAddress ??
                                                                              '',
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                              child: const Center(
                                                                  child: Icon(
                                                                      Icons
                                                                          .place_outlined,
                                                                      size:
                                                                          22)),
                                                            ),
                                                          ),
                                                        ),
                                                    ]),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                              // Liste items éditables
                              SliverPadding(
                                padding:
                                    const EdgeInsets.fromLTRB(14, 4, 14, 240),
                                sliver: (items.isEmpty)
                                    ? SliverToBoxAdapter(
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 40),
                                          child: Center(
                                            child: Column(
                                              children: [
                                                Icon(Icons.remove_shopping_cart,
                                                    size: 64,
                                                    color:
                                                        Colors.grey.shade400),
                                                const SizedBox(height: 12),
                                                Text(
                                                  'all_items_removed'.tr.isEmpty
                                                      ? 'All items have been removed'
                                                      : 'all_items_removed'.tr,
                                                  style: TextStyle(
                                                      color:
                                                          Colors.grey.shade700,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      )
                                    : SliverList.separated(
                                        itemCount: items.length,
                                        separatorBuilder: (_, __) =>
                                            const SizedBox(height: 10),
                                        itemBuilder: (_, i) =>
                                            _EditableStoreItemRow(
                                          order: _currentOrder,
                                          orderItem: items[i],
                                          orderController: orderController,
                                          onRemove: _showRemoveItemDialog,
                                        ),
                                      ),
                              ),
                            ],
                          ),
                        ),

                        // Barre récap collante (Validate / Decline)
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: _BottomValidationBar(
                            productsTotal: productsTotal,
                            delivery: delivery,
                            total: total,
                            isValidating: isValidating,
                            isDeclining: isDeclining,
                            onValidate: (isValidating || isDeclining)
                                ? null
                                : () async {
                                    setState(() => isValidating = true);
                                    try {
                                      await orderController
                                          .validateOrderWithModifications(
                                              widget.order.orderId!);
                                      if (!mounted) return;
                                      Get.off(() => ZoomDrawerWrapper(
                                          child: const MyStoreOrdersPage(),
                                          shouldOpenDrawer: true));
                                    } catch (e) {
                                      if (!mounted) return;
                                      Get.snackbar(
                                        'error'.tr.isEmpty
                                            ? 'Error'
                                            : 'error'.tr,
                                        ('validate_failed'.tr.isEmpty
                                                ? 'Failed to validate order'
                                                : 'validate_failed'.tr) +
                                            ': $e',
                                        snackPosition: SnackPosition.BOTTOM,
                                        backgroundColor: Colors.red,
                                        colorText: Colors.white,
                                      );
                                    } finally {
                                      if (mounted)
                                        setState(() => isValidating = false);
                                    }
                                  },
                            onDecline: (isValidating || isDeclining)
                                ? null
                                : () async {
                                    setState(() => isDeclining = true);
                                    try {
                                      await orderController
                                          .declineOrder(widget.order.orderId!);
                                      if (!mounted) return;
                                      Get.off(() => ZoomDrawerWrapper(
                                          child: const MyStoreOrdersPage(),
                                          shouldOpenDrawer: true));
                                    } catch (e) {
                                      if (!mounted) return;
                                      Get.snackbar(
                                        'error'.tr.isEmpty
                                            ? 'Error'
                                            : 'error'.tr,
                                        ('decline_failed'.tr.isEmpty
                                                ? 'Failed to decline order'
                                                : 'decline_failed'.tr) +
                                            ': $e',
                                        snackPosition: SnackPosition.BOTTOM,
                                        backgroundColor: Colors.red,
                                        colorText: Colors.white,
                                      );
                                    } finally {
                                      if (mounted)
                                        setState(() => isDeclining = false);
                                    }
                                  },
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/* ===================== Row item éditable (store) ===================== */
class _EditableStoreItemRow extends StatefulWidget {
  final Order order;
  final OrderItem orderItem;
  final OrderController orderController;
  final void Function(int itemId) onRemove;

  const _EditableStoreItemRow({
    required this.order,
    required this.orderItem,
    required this.orderController,
    required this.onRemove,
    Key? key,
  }) : super(key: key);

  @override
  State<_EditableStoreItemRow> createState() => _EditableStoreItemRowState();
}

class _EditableStoreItemRowState extends State<_EditableStoreItemRow> {
  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  int get _currentQty {
    final key = '${widget.order.orderId}_${widget.orderItem.itemId}';
    return widget.orderController.modifiedQuantities[key] ?? widget.orderItem.quantity;
  }

  void _onQtyChanged(int newQty) {
    if (newQty < 1) return;

    // Update immediately in the controller
    widget.orderController.updateOrderItemQuantity(
      widget.order.orderId!,
      widget.orderItem.itemId,
      newQty,
    );
  }

  String get _itemName {
    // Debug print to see what data we have
    print('OrderItem data: title=${widget.orderItem.title}, name=${widget.orderItem.name}, itemId=${widget.orderItem.itemId}');

    // Try multiple sources for the item name
    if (widget.orderItem.title?.isNotEmpty == true) {
      return widget.orderItem.title!;
    }
    if (widget.orderItem.name?.isNotEmpty == true) {
      return widget.orderItem.name!;
    }

    // Try to get the title using localization method
    try {
      final localizedTitle = widget.orderItem.getTitle();
      if (localizedTitle.isNotEmpty) {
        return localizedTitle;
      }
    } catch (e) {
      print('Error getting localized title: $e');
    }

    return 'unknown_item'.tr.isEmpty ? 'Unknown Item' : 'unknown_item'.tr;
  }

  String? get _itemImageUrl {
    print('OrderItem images: ${widget.orderItem.images}, imageUrl=${widget.orderItem.imageUrl}');

    if (widget.orderItem.images != null && widget.orderItem.images!.isNotEmpty) {
      final imageUrl = widget.orderItem.images![0].toString();
      print('Using image from images array: $imageUrl');
      return imageUrl;
    }
    if (widget.orderItem.imageUrl?.isNotEmpty == true) {
      print('Using imageUrl field: ${widget.orderItem.imageUrl}');
      return widget.orderItem.imageUrl;
    }
    print('No image found for item');
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final price = (widget.orderItem.price ?? 0).toDouble();

    return Obx(() {
      final currentQty = _currentQty;
      final lineTotal = price * currentQty;
      final itemName = _itemName;
      final imageUrl = _itemImageUrl;

      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: Colors.grey.withOpacity(0.12)),
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Ligne image + infos + bouton supprimer
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: imageUrl != null
                      ? Image.network(
                          imageUrl,
                          width: 70,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 70,
                              height: 80,
                              color: Colors.grey.shade200,
                              child: Icon(Icons.image, color: Colors.grey.shade600),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              width: 70,
                              height: 80,
                              color: Colors.grey.shade200,
                              child: Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                            );
                          },
                        )
                      : Container(
                          width: 70,
                          height: 80,
                          color: Colors.grey.shade200,
                          child: Icon(Icons.image, color: Colors.grey.shade600),
                        ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(itemName,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontWeight: FontWeight.w600)),
                        const SizedBox(height: 6),
                        Wrap(spacing: 8, runSpacing: 6, children: [
                          _MiniPill(
                              text:
                                  '${'price'.tr.isEmpty ? 'Price' : 'price'.tr}: ${price.toStringAsFixed(2)} ${'mru'.tr}'),
                          _MiniPill(
                              text:
                                  '${'quantity'.tr.isEmpty ? 'Quantity' : 'quantity'.tr}: $currentQty'),
                        ]),
                      ]),
                ),
                IconButton(
                  tooltip: 'remove'.tr.isEmpty ? 'Remove' : 'remove'.tr,
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: () => widget.onRemove(widget.orderItem.itemId),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Ligne quantité avec stepper + total
            Row(
              children: [
                Text(
                  '${'quantity'.tr.isEmpty ? 'Quantity' : 'quantity'.tr}: ',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(width: 8),
                Container(
                  height: 38,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Minus button
                      SizedBox(
                        width: 38,
                        height: 38,
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(10),
                            onTap: currentQty > 1 ? () => _onQtyChanged(currentQty - 1) : null,
                            child: Icon(
                              Icons.remove_rounded,
                              size: 20,
                              color: currentQty > 1 ? Colors.black87 : Colors.black26,
                            ),
                          ),
                        ),
                      ),
                      // Value
                      SizedBox(
                        width: 44,
                        child: Center(
                          child: Text(
                            '$currentQty',
                            style: const TextStyle(fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                      // Plus button
                      SizedBox(
                        width: 38,
                        height: 38,
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(10),
                            onTap: () => _onQtyChanged(currentQty + 1),
                            child: const Icon(
                              Icons.add_rounded,
                              size: 20,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Text(
                  '${'total'.tr.isEmpty ? 'Total' : 'total'.tr}: ${lineTotal.toStringAsFixed(2)} ${'mru'.tr}',
                  style: const TextStyle(fontWeight: FontWeight.w700),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}

class _QtyStepper extends StatefulWidget {
  final int initial;
  final int min;
  final int? max;
  final ValueChanged<int> onChanged;

  const _QtyStepper({
    required this.initial,
    required this.onChanged,
    this.min = 1,
    this.max,
    Key? key,
  }) : super(key: key);

  @override
  State<_QtyStepper> createState() => _QtyStepperState();
}

class _QtyStepperState extends State<_QtyStepper> {
  Timer? _repeatTimer;

  @override
  void dispose() {
    _repeatTimer?.cancel();
    super.dispose();
  }

  int get _currentValue => widget.initial.clamp(widget.min, widget.max ?? 999999);

  void _change(int delta) {
    final next = (_currentValue + delta).clamp(widget.min, widget.max ?? 999999);
    if (next == _currentValue) return;
    widget.onChanged(next);
  }

  void _startAuto(int delta) {
    // lance la répétition automatique ±
    _repeatTimer?.cancel();
    _repeatTimer =
        Timer.periodic(const Duration(milliseconds: 90), (_) => _change(delta));
  }

  void _stopAuto() {
    _repeatTimer?.cancel();
    _repeatTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    final border = Border.all(color: Colors.grey.shade300);
    final currentValue = _currentValue;

    return Container(
      height: 38,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: border,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Minus
          _stepButton(
            icon: Icons.remove_rounded,
            delta: -1,
            enabled: currentValue > widget.min,
          ),

          // Value
          SizedBox(
            width: 44,
            child: Center(
              child: Text('$currentValue',
                  style: const TextStyle(fontWeight: FontWeight.w700)),
            ),
          ),

          // Plus
          _stepButton(
            icon: Icons.add_rounded,
            delta: 1,
            enabled: (widget.max == null) ? true : currentValue < widget.max!,
          ),
        ],
      ),
    );
  }

  Widget _stepButton({
    required IconData icon,
    required int delta,
    required bool enabled,
  }) {
    return SizedBox(
      width: 38,
      height: 38,
      child: GestureDetector(
        onTap: enabled ? () => _change(delta) : null,
        // Compatible large: démarre la répétition au long press
        onLongPress: enabled ? () => _startAuto(delta) : null,
        onLongPressUp: enabled ? _stopAuto : null,
        // Sécurité: stop si le doigt quitte
        onTapUp: (_) => _stopAuto(),
        onTapCancel: _stopAuto,
        child: Container(
          alignment: Alignment.center,
          child: Icon(icon,
              size: 20, color: enabled ? Colors.black87 : Colors.black26),
        ),
      ),
    );
  }
}

class _MiniPill extends StatelessWidget {
  final String text;
  const _MiniPill({required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(999),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Text(text,
          style: TextStyle(fontSize: 11, color: Colors.grey.shade800)),
    );
  }
}

/* ===================== Bottom recap (validate / decline) ===================== */
class _BottomValidationBar extends StatelessWidget {
  final double productsTotal;
  final double delivery;
  final double total;
  final bool isValidating;
  final bool isDeclining;
  final VoidCallback? onValidate;
  final VoidCallback? onDecline;

  const _BottomValidationBar({
    required this.productsTotal,
    required this.delivery,
    required this.total,
    required this.isValidating,
    required this.isDeclining,
    required this.onValidate,
    required this.onDecline,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 16,
      shadowColor: Colors.black.withOpacity(0.12),
      child: SafeArea(
        top: false,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border(top: BorderSide(color: Colors.grey.shade200))),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _row(
                  'products_total'.tr.isEmpty
                      ? 'Products total'
                      : 'products_total'.tr,
                  '${productsTotal.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row(
                  'delivery_charge'.tr.isEmpty
                      ? 'Delivery'
                      : 'delivery_charge'.tr,
                  '${delivery.toStringAsFixed(2)} ${'mru'.tr}'),
              const SizedBox(height: 8),
              _row('total'.tr, '${total.toStringAsFixed(2)} ${'mru'.tr}',
                  bold: true, color: AppColors.primary),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: onValidate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryVariant,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      child: isValidating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CupertinoActivityIndicator(
                                  color: Colors.white))
                          : Text('validate_order'.tr.isEmpty
                              ? 'Validate Order'
                              : 'validate_order'.tr),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: onDecline,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.error,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      child: isDeclining
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CupertinoActivityIndicator(
                                  color: Colors.white))
                          : Text('decline_order'.tr.isEmpty
                              ? 'Decline Order'
                              : 'decline_order'.tr),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _row(String label, String value, {bool bold = false, Color? color}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label),
        Text(value,
            style: TextStyle(
                fontWeight: bold ? FontWeight.w800 : FontWeight.w600,
                color: color))
      ],
    );
  }
}

class _GlassHeader extends StatelessWidget {
  final String title;
  const _GlassHeader({required this.title});

  void _navigateToHome() async {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.25),
              border: Border.all(color: Colors.white.withOpacity(0.35)),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                const SizedBox(width: 6),
                _GlassIconButton(
                  icon: Icons.arrow_back_ios_new_rounded,
                  onTap: () => _navigateToHome(),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(
                    width: 46), // équilibre visuel avec le bouton retour
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _GlassIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _GlassIconButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Material(
        color: Colors.white.withOpacity(0.18),
        child: InkWell(
          onTap: onTap,
          child: SizedBox(
            width: 40,
            height: 40,
            child: Icon(icon, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
