import 'package:get/get.dart';

class OrderModification {
  final String type;
  final String itemName;
  final int? oldQuantity;
  final int? newQuantity;
  final double? oldPrice;
  final double? newPrice;
  final String? reason;

  OrderModification({
    required this.type,
    required this.itemName,
    this.oldQuantity,
    this.newQuantity,
    this.oldPrice,
    this.newPrice,
    this.reason,
  });

  factory OrderModification.fromJson(Map<String, dynamic> json) {
    return OrderModification(
      type: json['type'] ?? '',
      itemName: json['item_name'] ?? '',
      oldQuantity: json['old_quantity'],
      newQuantity: json['new_quantity'],
      oldPrice: json['old_price'] != null ? double.tryParse(json['old_price'].toString()) : null,
      newPrice: json['new_price'] != null ? double.tryParse(json['new_price'].toString()) : null,
      reason: json['reason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'item_name': itemName,
      'old_quantity': oldQuantity,
      'new_quantity': newQuantity,
      'old_price': oldPrice,
      'new_price': newPrice,
      'reason': reason,
    };
  }
}

class OrderItem {
  final int itemId;
  final int quantity;
  final double? price;
  final double? originalPrice;
  final double? total;
  final String? name;
  final String? title;
  final String? titleAr;
  final String? description;
  final String? descriptionAr;
  final String? imageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  List<dynamic>? images;

  // Item context fields
  final String? condition;
  final int? brandId;
  final int? categoryId;
  final int? storeId;
  final String? brandName;
  final String? categoryName;
  final String? storeName;

  // Category details
  final List<Map<String, dynamic>>? categoryDetails;

  // Promotion fields
  final bool hasPromotion;
  final double? promotionPercentage;

  OrderItem({
    required this.itemId,
    required this.quantity,
    this.title,
    this.titleAr,
    this.price,
    this.originalPrice,
    this.total,
    this.name,
    this.description,
    this.descriptionAr,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
    this.images,
    this.condition,
    this.brandId,
    this.categoryId,
    this.storeId,
    this.brandName,
    this.categoryName,
    this.storeName,
    this.categoryDetails,
    this.hasPromotion = false,
    this.promotionPercentage,
  });

 factory OrderItem.fromJson(Map<String, dynamic> json) {
  return OrderItem(
    itemId: json['item_id'] ?? 0,
    quantity: json['quantity'] ?? 0,
    price: json['price'] != null ? double.tryParse(json['price'].toString()) : null,
    originalPrice: json['original_price'] != null ? double.tryParse(json['original_price'].toString()) : null,
    total: json['total'] != null ? double.tryParse(json['total'].toString()) : null,
    name: json['name'],
    title: json['title'],
    titleAr: json['title_ar'],
    description: json['description'],
    descriptionAr: json['description_ar'],
    imageUrl: json['image_url'],
    images: (json['images'] as List<dynamic>?)
        ?.map((image) => image.toString())
        .toList() ?? [],
    createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at']) : null,
    updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at']) : null,

    // Item context fields
    condition: json['condition'],
    brandId: json['brand_id'],
    categoryId: json['category_id'],
    storeId: json['store_id'],
    brandName: json['brand_name'],
    categoryName: json['category_name'],
    storeName: json['store_name'],

    // Category details
    categoryDetails: (json['category_details'] as List<dynamic>?)
        ?.map((detail) => Map<String, dynamic>.from(detail))
        .toList(),

    // Promotion fields
    hasPromotion: json['has_promotion'] ?? false,
    promotionPercentage: json['promotion_percentage'] != null
        ? double.tryParse(json['promotion_percentage'].toString())
        : null,
  );
}

  /// Get title based on current language
  String getTitle() {
    String languageCode = Get.locale?.languageCode ?? 'en';

    switch (languageCode) {
      case 'ar':
        return titleAr?.isNotEmpty == true ? titleAr! : (title ?? name ?? '');
      default:
        return title ?? name ?? '';
    }
  }

  /// Get description based on current language
  String getDescription() {
    String languageCode = Get.locale?.languageCode ?? 'en';

    switch (languageCode) {
      case 'ar':
        return descriptionAr?.isNotEmpty == true ? descriptionAr! : (description ?? '');
      default:
        return description ?? '';
    }
  }

  /// Get localized category details
  List<Map<String, dynamic>> getLocalizedCategoryDetails() {
    if (categoryDetails == null) return [];

    String languageCode = Get.locale?.languageCode ?? 'en';

    return categoryDetails!.map((detail) {
      String label = detail['label'] ?? '';

      // If we have localized labels in the detail
      if (detail.containsKey('label_en')) {
        switch (languageCode) {
          case 'ar':
            label = detail['label_ar'] ?? detail['label_en'] ?? '';
            break;
          case 'fr':
            label = detail['label_fr'] ?? detail['label_en'] ?? '';
            break;
          default:
            label = detail['label_en'] ?? '';
        }
      }

      return {
        'label': label,
        'value': detail['value'] ?? ''
      };
    }).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'item_id': itemId,
      'quantity': quantity,
      'name': name,
      'price': price,
      'title': title,
      'images': images
    };
  }
}




class Order {
  final int? orderId;
  final int storeId;
  final List<OrderItem> items;
  final String? status;
  final String? deliveryCharge;
  final int? deliveryChargeId;
  final int? locationId;
  final bool isCashOnDelivery;
  final bool isPaid;
  final double? totalOrders;
  final int? userId;
  final String? userFirstName;
  final String? userLastName;
  final String? userPhone;
  final String? paymentScreenshot;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? deliveryAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool modifiedByStore;
  final String? storeName;
  final String? storeImage;
  final String? modificationReasonEn;
  final String? modificationReasonFr;
  final String? modificationReasonAr;
  final List<OrderModification> modifications;

  Order({
    this.orderId,
    required this.storeId,
    required this.items,
    this.status,
    this.deliveryCharge,
    this.deliveryChargeId,
    this.locationId,
    this.isCashOnDelivery = false,
    this.isPaid = false,
    this.totalOrders,
    this.userId,
    this.userFirstName,
    this.userLastName,
    this.userPhone,
    this.paymentScreenshot,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.deliveryAddress,
    this.createdAt,
    this.updatedAt,
    this.modifiedByStore = false,
    this.storeName,
    this.storeImage,
    this.modificationReasonEn,
    this.modificationReasonFr,
    this.modificationReasonAr,
    this.modifications = const [],
  });

  // Get modification reason based on language
  String? getModificationReason(String language) {
    switch (language.toLowerCase()) {
      case 'fr':
        return modificationReasonFr;
      case 'ar':
        return modificationReasonAr;
      case 'en':
      default:
        return modificationReasonEn;
    }
  }

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      orderId: json['id'],
      storeId: json['store_id'] ?? 0,
      items: json['items'] != null 
          ? (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList()
          : [],
      status: json['status'] ?? '',
      deliveryCharge: json['delivery_charge'],
      deliveryChargeId: json['delivery_charge_id'],
      locationId: json['location_id'],
      isCashOnDelivery: json['is_cash_on_delivery'] ?? false,
      isPaid: json['is_paid'] ?? false,
      totalOrders: json['totalorders']?.toDouble(),
      userFirstName: json['userfirstname'],
      userLastName: json['userlastname'],
      userPhone:  json['userphone'],
      paymentScreenshot: json['payment_screenshot'],
      deliveryLatitude: json['delivery_latitude'] != null 
          ? double.tryParse(json['delivery_latitude'].toString())
          : null,
      deliveryLongitude: json['delivery_longitude'] != null 
          ? double.tryParse(json['delivery_longitude'].toString())
          : null,
      deliveryAddress: json['delivery_address'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
      modifiedByStore: json['modified_by_store'] ?? false,
      storeName: json['store_name'],
      storeImage: json['store_image'],
      modificationReasonEn: json['modification_reason_en'],
      modificationReasonFr: json['modification_reason_fr'],
      modificationReasonAr: json['modification_reason_ar'],
      modifications: json['modifications'] != null 
          ? (json['modifications'] as List).map((mod) => OrderModification.fromJson(mod)).toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'store_id': storeId,
      'items': items.map((item) => item.toJson()).toList(),
      'delivery_charge_id': deliveryChargeId,
      'location_id': locationId,
      'is_cash_on_delivery': isCashOnDelivery,
      'is_paid': isPaid,
      'phone' : userPhone
    };
  }
}























class DeliveryCharge {
  final int id;
  final String type;
  final String amount;
  

  DeliveryCharge({
    required this.id,
    required this.amount,
    required this.type,
  });

  
  factory DeliveryCharge.fromJson(Map<String, dynamic> json) {
    return DeliveryCharge(
      id: json['id'],
      amount: json['amount'],
      type: json['type']
    );
  }

  
}
