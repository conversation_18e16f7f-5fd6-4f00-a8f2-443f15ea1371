import 'dart:developer';
import 'dart:io';
import 'dart:convert';
import 'package:boutigak/data/models/location.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart';

import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/services/order_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;



class OrderItemController extends GetxController {
  RxInt itemId; // Only the item ID
  RxInt quantity = 0.obs; // Quantity of the item
  RxDouble? price;
  RxDouble? total;
var isLoading = true.obs;
  OrderItemController({
    required int initialItemId,
    required int initialQuantity,
    double initialPrice = 0.0,
    double initialTotal = 0.0,
  }) : itemId = initialItemId.obs {
    quantity.value = initialQuantity;
  }

  void incrementQuantity() {
    quantity.value++;
    update();
  }

  void decrementQuantity() {
    if (quantity.value > 1) {
      quantity.value--;
      update();
    }
  }

  void updateQuantity(int newQuantity) {
    quantity.value = newQuantity;
    update();
  }
}

class OrderController extends GetxController {
  RxInt orderId = 0.obs;
  RxList<OrderItemController> items = <OrderItemController>[].obs; // List of item controllers in the order
  RxString status = 'Pending'.obs;
  RxInt deliveryChargeID = 0.obs;
  RxDouble total = 0.0.obs;
  RxList<DeliveryCharge> deliveryCharges = <DeliveryCharge>[].obs;
  RxList<Order> myOrders = <Order>[].obs;
  var myStoreOrders = <Order>[].obs;
  var isLoading = false.obs;
  var paymentStatusFilter = ''.obs;

  // Pagination variables for my orders
  var currentPage = 1.obs;
  var hasMoreOrders = true.obs;
  var isLoadingMore = false.obs;
  final int perPage = 10;
  RxBool isCashOnDelivery = false.obs;
  RxBool isLocationSelected = false.obs; // Added isLocationSelected
  final RxMap<String, int> modifiedQuantities = <String, int>{}.obs;
  final RxSet<String> removedItems = <String>{}.obs;
  final RxList<Location> userLocations = <Location>[].obs;
  final Rx<Location?> selectedLocation = Rx<Location?>(null);
  var newLocationName = ''.obs;
  var newLocationAddress = ''.obs;
  
  // Track item listeners to avoid duplicates
  final Set<int> _itemListeners = <int>{};

  OrderController({
    int initialOrderId = 0,
    List<OrderItemController>? initialItems,
    String initialStatus = 'Pending',
  }) {
    orderId.value = initialOrderId;
    items.addAll(initialItems ?? []);
    status.value = initialStatus;
  }

  Rx<LatLng> currentPosition = Rx<LatLng>(LatLng(18.0731, -15.9582));

  void updateLocation(LatLng newPosition) {
    currentPosition.value = newPosition;
    isLocationSelected.value = true; // Update isLocationSelected when location is set
    update();
  }

  void _setupItemListener(int itemId) {
    // Avoid setting up duplicate listeners
    if (_itemListeners.contains(itemId)) return;
    
    _itemListeners.add(itemId);
    final tag = itemId.toString();
    final itemCtrl = Get.put(ItemController(), tag: tag);
    
    // Listen for when the item loads and recalculate total
    ever(itemCtrl.selectedItem, (_) {
      if (itemCtrl.selectedItem.value != null) {
        calculateTotal();
      }
    });
  }

  @override
  void onInit() {
    super.onInit();
    // fetchUserLocations();
    
    // Set up listeners for existing items
    for (var item in items) {
      _setupItemListener(item.itemId.value);
    }
    
    calculateTotal();
    fetchDeliveryCharge();

    ever<int>(deliveryChargeID, (_) => calculateTotal());

    ever<List<OrderItemController>>(items, (_) => calculateTotal());
  }

  /// Fetch delivery charges
  Future<void> fetchDeliveryCharge() async {
    try {
      var fetchedDeliveryCharge = await OrderService.fetchDeliveryCharge();
      if (fetchedDeliveryCharge != null) {
        deliveryCharges.clear();
        deliveryCharges.addAll(fetchedDeliveryCharge);
      } else {
      //  // Get.snakbar("Error", "Failed to load delivery charges");
      }
    } catch (e) {
      Get.snackbar("Error", "An error occurred: \${e.toString()}");
    }
  }

  void addItem(OrderItemController newItemController) {
    var existingItemController = items.firstWhereOrNull(
      (item) => item.itemId.value == newItemController.itemId.value,
    );

    if (existingItemController != null) {
      existingItemController.updateQuantity(
        existingItemController.quantity.value + newItemController.quantity.value,
      );
    } else {
      items.add(newItemController);
      // Set up listener for when the item loads
      _setupItemListener(newItemController.itemId.value);
    }

    calculateTotal();
    update();
  }

  void removeItem(int itemId) {
    items.removeWhere((item) => item.itemId.value == itemId);
    calculateTotal();
    update();
  }

  void clearOrder() {
    items.clear();
    calculateTotal();
    update();
  }

  void calculateTotal() {
  double newTotal = 0.0;

  // Items total
  for (var oc in items) {
    final tag = oc.itemId.value.toString();
    final itemCtrl = Get.put(ItemController(), tag: tag);

    // si pas encore chargé, on déclenche le fetch (une seule fois)
    if (itemCtrl.selectedItem.value == null) {
      itemCtrl.fetchItemById(oc.itemId.value);
    }

    final item = itemCtrl.selectedItem.value;
    // fallback sur oc.price si l'item n'est pas encore chargé
    final unitPrice = (item != null) ? item.price : (oc.price?.value ?? 0.0);
    newTotal += unitPrice * oc.quantity.value;
  }

  // Delivery
  final dc = deliveryCharges.firstWhereOrNull((c) => c.id == deliveryChargeID.value);
  final deliveryAmount = _asDouble(dc?.amount); // 👈 voir helper ci-dessous
  newTotal += deliveryAmount;

  total.value = newTotal;
  update();
}


  Future<void> fetchUserLocations() async {
    try {
      List<Location>? locations = await OrderService.fetchUserLocations();
      if (locations != null) {
        userLocations.value = locations;
      }
    } catch (e) {
      print('Error fetching locations: $e');
    }
  }

  Future<void> saveNewLocation() async {
    if (!isLocationSelected.value) {
      Get.snackbar(
        'Error',
        'Please select a location on the map',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      
      Map<String, dynamic> locationData = {
        'name': newLocationName.value,
        'address': newLocationAddress.value,
        'latitude': currentPosition.value.latitude.toString(),
        'longitude': currentPosition.value.longitude.toString(),
      };

      var response = await OrderService.storeUserLocation(locationData);

      if (response != null) {
        // Create a new Location object
        Location newLocation = Location(
          id: response['location_id'],
          name: newLocationName.value,
          address: newLocationAddress.value,
          latitude: currentPosition.value.latitude,
          longitude: currentPosition.value.longitude,
        );
        
        // Add to user locations
        userLocations.add(newLocation);
        
        // Clear form
        newLocationName.value = '';
        newLocationAddress.value = '';
        
        Get.snackbar(
          'Success',
          'Location saved successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save location',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      print('Error saving location: $e');
    }
  }

double _asDouble(dynamic v) {
    if (v == null) return 0.0;
    if (v is num) return v.toDouble();
    if (v is String) {
      final cleaned = v.replaceAll(RegExp(r'[^0-9\.\-]'), '');
      return double.tryParse(cleaned) ?? 0.0;
    }
    return 0.0;
  }

  // ---- Getters pratiques pour l'UI ----
  double get deliveryAmount {
    final dc = deliveryCharges.firstWhereOrNull((c) => c.id == deliveryChargeID.value);
    return dc == null ? 0.0 : _asDouble(dc.amount);
  }

  double get subtotal {
    double s = 0.0;
    for (var oc in items) {
      // Récupère le prix: item chargé OU fallback sur OrderItemController.price
      final itemCtrl = Get.put(ItemController(), tag: oc.itemId.value.toString());
      final item = itemCtrl.selectedItem.value;
      final unitPrice = (item != null) ? item.price : (oc.price?.value ?? 0.0);
      s += unitPrice * oc.quantity.value;
    }
    return s;
  }

  

  // ---- Recalcul propre et déterministe ----
  
  Future<bool> deleteLocation(int locationId) async {
    try {
      bool success = await OrderService.deleteUserLocation(locationId);
      
      if (success) {
        // Remove the location from the local list
        userLocations.removeWhere((location) => location.id == locationId);
        
        Get.snackbar(
          'Success',
          'Location deleted successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'Error',
          'Failed to delete location',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'An error occurred while deleting the location',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      print('Error deleting location: $e');
      return false;
    }
  }

  Future<bool> createOrder({required int storeId}) async {
    if (selectedLocation.value == null) {
      Get.snackbar('Error', 'Please select or create a delivery location');
      return false;
    }

    try {
      List<OrderItem> orderItems = items.map((itemController) => OrderItem(
        itemId: itemController.itemId.value,
        quantity: itemController.quantity.value,
      )).toList();



      print('Creating order with items: ${jsonEncode(orderItems)}');

      
      Order newOrder = Order(
        storeId: storeId,
        items: orderItems,
        deliveryChargeId: deliveryChargeID.value,
        locationId: selectedLocation.value!.id, 
        isCashOnDelivery: isCashOnDelivery.value
      );
      bool result = await OrderService.createOrder(newOrder);
      if (result) {
     //   // Get.snakbar("Success", "Order successfully created");
        return true;
      } else {
    //    // Get.snakbar("Error", "Failed to create order");
        return false;
      }
    } catch (e) {
      Get.snackbar("Error", "Error creating order: \${e.toString()}");
      return false;
    }
  }

Future<void> fetchedMyOrders({bool refresh = false}) async {
  if (refresh && isLoading.value) return;
  if (!refresh && isLoadingMore.value) return;

  if (refresh) {
    currentPage.value = 1;
    myOrders.clear();
    hasMoreOrders.value = true;
    isLoading.value = true;
  } else {
    // Loading more orders
    if (!hasMoreOrders.value) {
      print('No more orders to load');
      return;
    }
    isLoadingMore.value = true;
  }

  log('Fetching my orders for page: ${currentPage.value}');

  try {
    List<Order>? fetchedOrders = await OrderService.fetchMyOrders(
      page: currentPage.value,
      perPage: perPage,
    );

    if (fetchedOrders != null) {
      if (fetchedOrders.isEmpty) {
        print('No more orders received from server');
        hasMoreOrders.value = false;
      } else {
        print('Received ${fetchedOrders.length} new orders');
        for (var newOrder in fetchedOrders) {
          if (!myOrders.any((existingOrder) => existingOrder.orderId == newOrder.orderId)) {
            myOrders.add(newOrder);
          }
        }
        currentPage.value++;
      }
    }
  } catch (e) {
    print('Error fetching my orders: $e');
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
}

  Future<void> fetchMyStoreOrders({
    String? fromDate,
    String? toDate,
    String? paymentStatus,
  }) async {
    isLoading.value = true;
    try {
      Map<String, dynamic> queryParams = {};
      
      if (fromDate != null && fromDate.isNotEmpty) {
        queryParams['from_date'] = fromDate;
      }
      if (toDate != null && toDate.isNotEmpty) {
        queryParams['to_date'] = toDate;
      }
      if (paymentStatus != null && paymentStatus.isNotEmpty) {
        queryParams['is_paid'] = paymentStatus == 'paid' ? '1' : '0';
      }

      List<Order>? fetchedOrders = await OrderService.fetchMyStoreOrders(queryParams);
      if (fetchedOrders != null) {
        myStoreOrders.value = fetchedOrders;
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to fetch store orders');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> changeOrderStatus(int orderId, String status) async {
    bool success = await OrderService.updateOrderStatus(orderId, status);
    if (success) {
      fetchedMyOrders();
      fetchMyStoreOrders();
    }
  }

  void updateOrderItemQuantity(int orderId, int itemId, int newQuantity) {
    try {
      // Store the modified quantity in the map
      String key = '${orderId}_${itemId}';
      modifiedQuantities[key] = newQuantity;

      // Force UI update
      update();

      print('Updated quantity for item $itemId to $newQuantity');
    } catch (e) {
      print('Error updating quantity: ${e.toString()}');
      Get.snackbar(
        'Error',
        'Failed to update item quantity',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void removeOrderItem(int orderId, int itemId) {
    try {
      var order = myStoreOrders.firstWhere((order) => order.orderId == orderId);
      var itemIndex = order.items.indexWhere((item) => item.itemId == itemId);
      
      if (itemIndex != -1) {
        // Mark item as removed
        String key = '${orderId}_${itemId}';
        removedItems.add(key);
        
        // Remove any existing quantity modifications
        modifiedQuantities.remove(key);
        
        update();
      }
    } catch (e) {
      print('Error removing item: ${e.toString()}');
      Get.snackbar(
        'Error',
        'Failed to remove item',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> validateOrderWithModifications(int orderId) async {
    try {
      var order = myStoreOrders.firstWhere((order) => order.orderId == orderId);
      
      // Create list of modified items, excluding removed items
      List<Map<String, dynamic>> modifiedItems = order.items
          .where((item) {
            String key = '${orderId}_${item.itemId}';
            return !removedItems.contains(key);
          })
          .map((item) {
            String key = '${orderId}_${item.itemId}';
            int quantity = modifiedQuantities[key] ?? item.quantity;
            
            return {
              'item_id': item.itemId,
              'quantity': quantity,
            };
          })
          .toList();

      // Validate that there's at least one item
      if (modifiedItems.isEmpty) {
        Get.snackbar(
          'Error',
          'Order must contain at least one item',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Validate that all quantities are valid
      bool hasInvalidQuantity = modifiedItems.any((item) => item['quantity'] <= 0);
      if (hasInvalidQuantity) {
        Get.snackbar(
          'Error',
          'All quantities must be greater than 0',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }


      log('modified items ${modifiedItems}');

      final response = await OrderService.validateOrderWithModifications(
        orderId,
        modifiedItems,
      );

      log('modified response  ${response}');


      
      if (response) {
        // Clear all modifications for this order
        modifiedQuantities.removeWhere((key, value) => key.startsWith('${orderId}_'));
        removedItems.removeWhere((key) => key.startsWith('${orderId}_'));
        
        // Refresh orders list
        await fetchMyStoreOrders();
        
        Get.back(); // Return to previous screen
      }
    } catch (e) {
      print('Error validating order: ${e.toString()}');
      Get.snackbar(
        'Error',
        'Failed to validate order',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> declineOrder(int orderId) async {
    try {
      final response = await OrderService.declineOrder(orderId);
      
      if (response) {
        // Clear modified quantities for this order
        modifiedQuantities.removeWhere((key, value) => key.startsWith('${orderId}_'));
        
        // Refresh orders list
        await fetchMyStoreOrders();
        
        Get.back(); // Return to previous screen
      }
    } catch (e) {
      print('Error declining order: ${e.toString()}');
      Get.snackbar(
        'Error',
        'Failed to decline order',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<bool> orderPayment({
    required int providerId,
    required int order_id,




    required double amount,



    required File paymentImage,
  }) async {
    try {


      print('in order payment');
      // Get the token
      String token = await WebService.getToken();

      // Create a multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${getCurrentBackendURL()}api/payment-order'), // Replace with your API endpoint
      );

      // Add headers
      request.headers['Authorization'] = token;
      request.headers['Accept'] = 'application/json';

      // Add fields
      request.fields['order_id'] = order_id.toString();
      request.fields['provider_id'] = providerId.toString();
      request.fields['amount'] = amount.toString();
      print('request ${request}');


      var imageStream = http.ByteStream(paymentImage.openRead());
      var imageLength = await paymentImage.length();
      var multipartFile = http.MultipartFile(
        'payment_image', // Field name for the image
        imageStream,
        imageLength,
        filename: basename(paymentImage.path),
      );
      request.files.add(multipartFile);

      // Send the request
      var response = await request.send();


      print('response upload image ${response}');

      // Check the response
      if (response.statusCode == 201) {
        Get.snackbar("Success", "Payment added successfully");
        return true;
      } else {
        Get.snackbar("Error", "Failed to add payment: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      Get.snackbar("Error", "An error occurred while adding payment: ${e.toString()}");
      return false;
    }
  }

  bool isItemModified(int orderId, int itemId) {
    String key = '${orderId}_${itemId}';
    return modifiedQuantities.containsKey(key) || removedItems.contains(key);
  }

  int getModifiedQuantity(int orderId, int itemId, int originalQuantity) {
    String key = '${orderId}_${itemId}';
    return modifiedQuantities[key] ?? originalQuantity;
  }

  bool isItemRemoved(int orderId, int itemId) {
    String key = '${orderId}_${itemId}';
    return removedItems.contains(key);
  }

  /// Clear all modifications for a specific order
  void clearOrderModifications(int orderId) {
    modifiedQuantities.removeWhere((key, value) => key.startsWith('${orderId}_'));
    removedItems.removeWhere((key) => key.startsWith('${orderId}_'));
    update();
  }
}
